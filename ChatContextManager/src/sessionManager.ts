import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface ChatMessage {
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
}

export interface ChatSession {
    id: string;
    name: string;
    messages: ChatMessage[];
    timestamp: number;
    summary?: string;
}

export class SessionManager {
    private sessionsFilePath: string;

    constructor(private context: vscode.ExtensionContext) {
        this.sessionsFilePath = path.join(context.globalStorageUri.fsPath, 'sessions.json');
        this.ensureStorageDirectory();
    }

    private ensureStorageDirectory() {
        const storageDir = path.dirname(this.sessionsFilePath);
        if (!fs.existsSync(storageDir)) {
            fs.mkdirSync(storageDir, { recursive: true });
        }
    }

    async saveSession(name: string, chatContent: string): Promise<void> {
        const messages = this.parseChatContent(chatContent);
        const session: ChatSession = {
            id: this.generateId(),
            name,
            messages,
            timestamp: Date.now()
        };

        const sessions = await this.getAllSessions();
        sessions.push(session);
        
        await this.saveSessions(sessions);
    }

    async getAllSessions(): Promise<ChatSession[]> {
        try {
            if (!fs.existsSync(this.sessionsFilePath)) {
                return [];
            }
            
            const data = fs.readFileSync(this.sessionsFilePath, 'utf8');
            return JSON.parse(data) || [];
        } catch (error) {
            console.error('Error reading sessions:', error);
            return [];
        }
    }

    async deleteSession(sessionId: string): Promise<void> {
        const sessions = await this.getAllSessions();
        const filteredSessions = sessions.filter(s => s.id !== sessionId);
        await this.saveSessions(filteredSessions);
    }

    previewMessages(chatContent: string): ChatMessage[] {
        return this.parseChatContent(chatContent);
    }

    generateMegaPrompt(session: ChatSession): string {
        let prompt = `# Previous Chat Context\n\n`;
        prompt += `Session: ${session.name}\n`;
        prompt += `Date: ${new Date(session.timestamp).toLocaleString()}\n\n`;
        
        prompt += `## Conversation History:\n\n`;
        
        session.messages.forEach((message, index) => {
            const role = message.role === 'user' ? 'User' : 'Assistant';
            prompt += `**${role}:** ${message.content}\n\n`;
        });
        
        prompt += `## Instructions:\n`;
        prompt += `Please continue this conversation with the same context and understanding. `;
        prompt += `Maintain the same tone and refer back to previous points discussed as needed.\n\n`;
        
        return prompt;
    }

    private parseChatContent(content: string): ChatMessage[] {
        const messages: ChatMessage[] = [];

        // If no clear chat format is found, treat as unstructured content
        const hasStructuredFormat = this.hasStructuredChatFormat(content);

        if (!hasStructuredFormat) {
            return this.parseUnstructuredContent(content);
        }

        // Parse structured content
        const lines = content.split('\n');
        let currentMessage: Partial<ChatMessage> = {};

        for (const line of lines) {
            const trimmedLine = line.trim();

            // Enhanced parsing for different chat formats
            if (this.isUserMessage(trimmedLine)) {
                if (currentMessage.content) {
                    messages.push(currentMessage as ChatMessage);
                }
                currentMessage = {
                    role: 'user',
                    content: this.extractMessageContent(trimmedLine, 'user'),
                    timestamp: Date.now()
                };
            } else if (this.isAssistantMessage(trimmedLine)) {
                if (currentMessage.content) {
                    messages.push(currentMessage as ChatMessage);
                }
                currentMessage = {
                    role: 'assistant',
                    content: this.extractMessageContent(trimmedLine, 'assistant'),
                    timestamp: Date.now()
                };
            } else if (trimmedLine && currentMessage.role) {
                currentMessage.content += '\n' + trimmedLine;
            }
        }

        if (currentMessage.content) {
            messages.push(currentMessage as ChatMessage);
        }

        return messages;
    }

    private hasStructuredChatFormat(content: string): boolean {
        const lines = content.split('\n');
        return lines.some(line =>
            this.isUserMessage(line.trim()) || this.isAssistantMessage(line.trim())
        );
    }

    private parseUnstructuredContent(content: string): ChatMessage[] {
        const messages: ChatMessage[] = [];

        // For unstructured content, create a user message and ask for assistant response
        const trimmedContent = content.trim();

        if (trimmedContent.length > 0) {
            // Add the content as user input
            messages.push({
                role: 'user',
                content: trimmedContent,
                timestamp: Date.now()
            });

            // Add a placeholder for assistant response
            messages.push({
                role: 'assistant',
                content: '[Please paste the AI assistant response here]',
                timestamp: Date.now()
            });
        }

        return messages;
    }

    private async saveSessions(sessions: ChatSession[]): Promise<void> {
        try {
            fs.writeFileSync(this.sessionsFilePath, JSON.stringify(sessions, null, 2));
        } catch (error) {
            throw new Error(`Failed to save sessions: ${error}`);
        }
    }

    private generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    private isUserMessage(line: string): boolean {
        const userPatterns = [
            /^user\s*:/i,
            /^👤/,
            /^human\s*:/i,
            /^\*\*user\*\*/i,
            /^\*\*human\*\*/i,
            /^you\s*:/i,
            /^me\s*:/i,
            /^>\s*user/i,
            /^>\s*human/i
        ];
        return userPatterns.some(pattern => pattern.test(line));
    }

    private isAssistantMessage(line: string): boolean {
        const assistantPatterns = [
            /^assistant\s*:/i,
            /^🤖/,
            /^ai\s*:/i,
            /^\*\*assistant\*\*/i,
            /^\*\*ai\*\*/i,
            /^augment\s*:/i,
            /^claude\s*:/i,
            /^gpt\s*:/i,
            /^>\s*assistant/i,
            /^>\s*ai/i
        ];
        return assistantPatterns.some(pattern => pattern.test(line));
    }

    private extractMessageContent(line: string, role: 'user' | 'assistant'): string {
        // Remove common prefixes
        const cleanPatterns = [
            /^(user|human|you|me)\s*:\s*/i,
            /^(assistant|ai|augment|claude|gpt)\s*:\s*/i,
            /^👤\s*/,
            /^🤖\s*/,
            /^\*\*(user|human|assistant|ai)\*\*\s*/i,
            /^>\s*(user|human|assistant|ai)\s*/i
        ];

        let content = line;
        for (const pattern of cleanPatterns) {
            content = content.replace(pattern, '');
        }

        return content.trim();
    }

    private extractRequestIds(content: string): string[] {
        const requestIds: string[] = [];
        // Look for UUID patterns (like the ones you mentioned)
        const uuidPattern = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi;
        const matches = content.match(uuidPattern);

        if (matches) {
            requestIds.push(...matches);
        }

        return [...new Set(requestIds)]; // Remove duplicates
    }

    private formatForTxtFile(messages: ChatMessage[], requestIds: string[], sessionName: string): string {
        let content = `# Chat Session: ${sessionName}\n`;
        content += `# Date: ${new Date().toLocaleString()}\n`;
        content += `# Messages: ${messages.length}\n`;

        if (requestIds.length > 0) {
            content += `# Request IDs: ${requestIds.join(', ')}\n`;
        }

        content += `\n${'='.repeat(50)}\n\n`;

        messages.forEach((message, index) => {
            content += `## Message ${index + 1} - ${message.role.toUpperCase()}\n`;
            content += `**Time:** ${new Date(message.timestamp).toLocaleString()}\n\n`;
            content += `${message.content}\n\n`;
            content += `${'-'.repeat(30)}\n\n`;
        });

        return content;
    }
}
