import * as vscode from 'vscode';

export class ChatMonitor {
    constructor() {}

    async getCurrentChatContent(): Promise<string | null> {
        try {
            // Method 1: Try to get content from active text editor (if user has selected chat content)
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor && activeEditor.selection && !activeEditor.selection.isEmpty) {
                const selectedText = activeEditor.document.getText(activeEditor.selection);
                if (this.looksLikeChatContent(selectedText)) {
                    return selectedText;
                }
            }

            // Method 2: Try to get content from clipboard (user can copy chat content first)
            const clipboardContent = await vscode.env.clipboard.readText();
            if (clipboardContent && this.looksLikeChatContent(clipboardContent)) {
                const shouldUseClipboard = await vscode.window.showQuickPick(
                    ['Yes', 'No'], 
                    { 
                        placeHolder: 'Use clipboard content as chat session?',
                        ignoreFocusOut: true
                    }
                );
                
                if (shouldUseClipboard === 'Yes') {
                    return clipboardContent;
                }
            }

            // Method 3: Ask user to manually input or paste content
            const manualInput = await vscode.window.showInputBox({
                prompt: 'Please paste your chat content here, or copy it to clipboard first and run the command again',
                placeHolder: 'Paste chat conversation here...',
                ignoreFocusOut: true
            });

            return manualInput || null;

        } catch (error) {
            console.error('Error getting chat content:', error);
            return null;
        }
    }

    private looksLikeChatContent(content: string): boolean {
        if (!content || content.length < 10) {
            return false;
        }

        // Check for common chat patterns
        const chatPatterns = [
            /user:/i,
            /assistant:/i,
            /👤/,
            /🤖/,
            /human:/i,
            /ai:/i,
            /you:/i,
            /me:/i
        ];

        return chatPatterns.some(pattern => pattern.test(content));
    }

    // Future enhancement: Monitor specific VSCode panels/webviews
    // This would require more complex implementation to hook into
    // specific AI extension panels like Cursor, Augment, etc.
    private async monitorAIExtensionPanels(): Promise<string | null> {
        // This is a placeholder for future implementation
        // Would need to:
        // 1. Detect active AI extension panels
        // 2. Extract content from their webviews
        // 3. Parse the conversation structure
        
        // For now, we rely on user interaction (selection/clipboard)
        return null;
    }
}
