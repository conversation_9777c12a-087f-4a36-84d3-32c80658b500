"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionManager = void 0;
const fs = require("fs");
const path = require("path");
class SessionManager {
    constructor(context) {
        this.context = context;
        this.sessionsFilePath = path.join(context.globalStorageUri.fsPath, 'sessions.json');
        this.savesMsgDir = path.join(context.globalStorageUri.fsPath, 'savesmsg');
        this.ensureStorageDirectory();
    }
    ensureStorageDirectory() {
        const storageDir = path.dirname(this.sessionsFilePath);
        if (!fs.existsSync(storageDir)) {
            fs.mkdirSync(storageDir, { recursive: true });
        }
        // Ensure savesmsg directory exists
        if (!fs.existsSync(this.savesMsgDir)) {
            fs.mkdirSync(this.savesMsgDir, { recursive: true });
        }
    }
    async saveSession(name, chatContent) {
        const messages = this.parseChatContent(chatContent);
        const requestIds = this.extractRequestIds(chatContent);
        // Create TXT file
        const txtFileName = `${this.generateId()}_${name.replace(/[^a-zA-Z0-9]/g, '_')}.txt`;
        const txtFilePath = path.join(this.savesMsgDir, txtFileName);
        // Format content for TXT file
        const formattedContent = this.formatForTxtFile(messages, requestIds, name);
        fs.writeFileSync(txtFilePath, formattedContent, 'utf8');
        const session = {
            id: this.generateId(),
            name,
            messages,
            timestamp: Date.now(),
            requestIds,
            txtFilePath
        };
        const sessions = await this.getAllSessions();
        sessions.push(session);
        await this.saveSessions(sessions);
        return session;
    }
    parseAndPreviewMessages(chatContent) {
        return this.parseChatContent(chatContent);
    }
    async getAllSessions() {
        try {
            if (!fs.existsSync(this.sessionsFilePath)) {
                return [];
            }
            const data = fs.readFileSync(this.sessionsFilePath, 'utf8');
            return JSON.parse(data) || [];
        }
        catch (error) {
            console.error('Error reading sessions:', error);
            return [];
        }
    }
    async deleteSession(sessionId) {
        const sessions = await this.getAllSessions();
        const filteredSessions = sessions.filter(s => s.id !== sessionId);
        await this.saveSessions(filteredSessions);
    }
    generateMegaPrompt(session) {
        let prompt = `# Previous Chat Context\n\n`;
        prompt += `Session: ${session.name}\n`;
        prompt += `Date: ${new Date(session.timestamp).toLocaleString()}\n\n`;
        prompt += `## Conversation History:\n\n`;
        session.messages.forEach((message, index) => {
            const role = message.role === 'user' ? 'User' : 'Assistant';
            prompt += `**${role}:** ${message.content}\n\n`;
        });
        prompt += `## Instructions:\n`;
        prompt += `Please continue this conversation with the same context and understanding. `;
        prompt += `Maintain the same tone and refer back to previous points discussed as needed.\n\n`;
        return prompt;
    }
    parseChatContent(content) {
        const messages = [];
        const lines = content.split('\n');
        let currentMessage = {};
        for (const line of lines) {
            const trimmedLine = line.trim();
            // Simple parsing - look for patterns that indicate user vs assistant messages
            if (trimmedLine.toLowerCase().startsWith('user:') || trimmedLine.startsWith('👤')) {
                if (currentMessage.content) {
                    messages.push(currentMessage);
                }
                currentMessage = {
                    role: 'user',
                    content: trimmedLine.replace(/^(user:|👤)\s*/i, ''),
                    timestamp: Date.now()
                };
            }
            else if (trimmedLine.toLowerCase().startsWith('assistant:') || trimmedLine.startsWith('🤖')) {
                if (currentMessage.content) {
                    messages.push(currentMessage);
                }
                currentMessage = {
                    role: 'assistant',
                    content: trimmedLine.replace(/^(assistant:|🤖)\s*/i, ''),
                    timestamp: Date.now()
                };
            }
            else if (trimmedLine && currentMessage.role) {
                currentMessage.content += '\n' + trimmedLine;
            }
        }
        if (currentMessage.content) {
            messages.push(currentMessage);
        }
        return messages;
    }
    async saveSessions(sessions) {
        try {
            fs.writeFileSync(this.sessionsFilePath, JSON.stringify(sessions, null, 2));
        }
        catch (error) {
            throw new Error(`Failed to save sessions: ${error}`);
        }
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    extractRequestIds(content) {
        const requestIds = [];
        // Look for UUID patterns (like the ones you mentioned)
        const uuidPattern = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi;
        const matches = content.match(uuidPattern);
        if (matches) {
            requestIds.push(...matches);
        }
        return [...new Set(requestIds)]; // Remove duplicates
    }
    formatForTxtFile(messages, requestIds, sessionName) {
        let content = `# Chat Session: ${sessionName}\n`;
        content += `# Date: ${new Date().toLocaleString()}\n`;
        content += `# Messages: ${messages.length}\n`;
        if (requestIds.length > 0) {
            content += `# Request IDs: ${requestIds.join(', ')}\n`;
        }
        content += `\n${'='.repeat(50)}\n\n`;
        messages.forEach((message, index) => {
            content += `## Message ${index + 1} - ${message.role.toUpperCase()}\n`;
            content += `**Time:** ${new Date(message.timestamp).toLocaleString()}\n\n`;
            content += `${message.content}\n\n`;
            content += `${'-'.repeat(30)}\n\n`;
        });
        return content;
    }
}
exports.SessionManager = SessionManager;
//# sourceMappingURL=sessionManager.js.map