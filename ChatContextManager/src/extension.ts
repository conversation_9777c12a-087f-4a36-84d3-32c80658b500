import * as vscode from 'vscode';
import { SessionManager } from './sessionManager';
import { ChatMonitor } from './chatMonitor';
import { WebviewProvider } from './webviewProvider';

export function activate(context: vscode.ExtensionContext) {
    console.log('Chat Context Manager is now active!');

    const sessionManager = new SessionManager(context);
    const chatMonitor = new ChatMonitor();
    const webviewProvider = new WebviewProvider(context, sessionManager);

    // Register commands
    const saveSessionCommand = vscode.commands.registerCommand('chatContextManager.saveSession', async () => {
        try {
            const chatContent = await chatMonitor.getCurrentChatContent();
            if (chatContent) {
                const sessionName = await vscode.window.showInputBox({
                    prompt: 'Enter a name for this chat session',
                    placeHolder: 'My Chat Session'
                });
                
                if (sessionName) {
                    await sessionManager.saveSession(sessionName, chatContent);
                    vscode.window.showInformationMessage(`Session "${sessionName}" saved successfully!`);
                }
            } else {
                vscode.window.showWarningMessage('No active chat content found to save.');
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to save session: ${error}`);
        }
    });

    const showSessionsCommand = vscode.commands.registerCommand('chatContextManager.showSessions', () => {
        webviewProvider.showSessions();
    });

    const generateMegaPromptCommand = vscode.commands.registerCommand('chatContextManager.generateMegaPrompt', async () => {
        const sessions = await sessionManager.getAllSessions();
        if (sessions.length === 0) {
            vscode.window.showInformationMessage('No saved sessions found.');
            return;
        }

        const sessionItems = sessions.map(session => ({
            label: session.name,
            description: new Date(session.timestamp).toLocaleString(),
            session: session
        }));

        const selectedItem = await vscode.window.showQuickPick(sessionItems, {
            placeHolder: 'Select a session to generate mega prompt'
        });

        if (selectedItem) {
            const megaPrompt = sessionManager.generateMegaPrompt(selectedItem.session);
            await vscode.env.clipboard.writeText(megaPrompt);
            vscode.window.showInformationMessage('Mega prompt copied to clipboard!');
        }
    });

    context.subscriptions.push(
        saveSessionCommand,
        showSessionsCommand,
        generateMegaPromptCommand
    );
}

export function deactivate() {}
