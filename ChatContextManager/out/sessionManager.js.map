{"version": 3, "file": "sessionManager.js", "sourceRoot": "", "sources": ["../src/sessionManager.ts"], "names": [], "mappings": ";;;AACA,yBAAyB;AACzB,6BAA6B;AAgB7B,MAAa,cAAc;IAGvB,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAChD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QACpF,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAEO,sBAAsB;QAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAC5B,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;SACjD;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,WAAmB;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACpD,MAAM,OAAO,GAAgB;YACzB,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI;YACJ,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC7C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvB,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,IAAI;YACA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;gBACvC,OAAO,EAAE,CAAC;aACb;YAED,MAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAC5D,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;SACjC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB;QACjC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC7C,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QAClE,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;IAC9C,CAAC;IAED,eAAe,CAAC,WAAmB;QAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;IAED,kBAAkB,CAAC,OAAoB;QACnC,IAAI,MAAM,GAAG,6BAA6B,CAAC;QAC3C,MAAM,IAAI,YAAY,OAAO,CAAC,IAAI,IAAI,CAAC;QACvC,MAAM,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC;QAEtE,MAAM,IAAI,8BAA8B,CAAC;QAEzC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YACxC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;YAC5D,MAAM,IAAI,KAAK,IAAI,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,oBAAoB,CAAC;QAC/B,MAAM,IAAI,6EAA6E,CAAC;QACxF,MAAM,IAAI,mFAAmF,CAAC;QAE9F,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,OAAe;QACpC,MAAM,QAAQ,GAAkB,EAAE,CAAC;QAEnC,kEAAkE;QAClE,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAElE,IAAI,CAAC,mBAAmB,EAAE;YACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;SACjD;QAED,2BAA2B;QAC3B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,cAAc,GAAyB,EAAE,CAAC;QAE9C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACtB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAEhC,8CAA8C;YAC9C,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE;gBACjC,IAAI,cAAc,CAAC,OAAO,EAAE;oBACxB,QAAQ,CAAC,IAAI,CAAC,cAA6B,CAAC,CAAC;iBAChD;gBACD,cAAc,GAAG;oBACb,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,MAAM,CAAC;oBACxD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACxB,CAAC;aACL;iBAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE;gBAC7C,IAAI,cAAc,CAAC,OAAO,EAAE;oBACxB,QAAQ,CAAC,IAAI,CAAC,cAA6B,CAAC,CAAC;iBAChD;gBACD,cAAc,GAAG;oBACb,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,WAAW,CAAC;oBAC7D,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACxB,CAAC;aACL;iBAAM,IAAI,WAAW,IAAI,cAAc,CAAC,IAAI,EAAE;gBAC3C,cAAc,CAAC,OAAO,IAAI,IAAI,GAAG,WAAW,CAAC;aAChD;SACJ;QAED,IAAI,cAAc,CAAC,OAAO,EAAE;YACxB,QAAQ,CAAC,IAAI,CAAC,cAA6B,CAAC,CAAC;SAChD;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,uBAAuB,CAAC,OAAe;QAC3C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAC1E,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,OAAe;QAC5C,MAAM,QAAQ,GAAkB,EAAE,CAAC;QAEnC,iFAAiF;QACjF,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEtC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,gCAAgC;YAChC,QAAQ,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,cAAc;gBACvB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC,CAAC;YAEH,2CAA2C;YAC3C,QAAQ,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,+CAA+C;gBACxD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC,CAAC;SACN;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAuB;QAC9C,IAAI;YACA,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;SAC9E;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;SACxD;IACL,CAAC;IAEO,UAAU;QACd,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAEO,aAAa,CAAC,IAAY;QAC9B,MAAM,YAAY,GAAG;YACjB,YAAY;YACZ,KAAK;YACL,aAAa;YACb,gBAAgB;YAChB,iBAAiB;YACjB,WAAW;YACX,UAAU;YACV,YAAY;YACZ,aAAa;SAChB,CAAC;QACF,OAAO,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,kBAAkB,CAAC,IAAY;QACnC,MAAM,iBAAiB,GAAG;YACtB,iBAAiB;YACjB,KAAK;YACL,UAAU;YACV,qBAAqB;YACrB,cAAc;YACd,eAAe;YACf,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,UAAU;SACb,CAAC;QACF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,IAA0B;QAClE,yBAAyB;QACzB,MAAM,aAAa,GAAG;YAClB,8BAA8B;YAC9B,4CAA4C;YAC5C,QAAQ;YACR,QAAQ;YACR,wCAAwC;YACxC,oCAAoC;SACvC,CAAC;QAEF,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;YACjC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SAC1C;QAED,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAEO,iBAAiB,CAAC,OAAe;QACrC,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,uDAAuD;QACvD,MAAM,WAAW,GAAG,gEAAgE,CAAC;QACrF,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,OAAO,EAAE;YACT,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;SAC/B;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,oBAAoB;IACzD,CAAC;IAEO,gBAAgB,CAAC,QAAuB,EAAE,UAAoB,EAAE,WAAmB;QACvF,IAAI,OAAO,GAAG,mBAAmB,WAAW,IAAI,CAAC;QACjD,OAAO,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC;QACtD,OAAO,IAAI,eAAe,QAAQ,CAAC,MAAM,IAAI,CAAC;QAE9C,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,OAAO,IAAI,kBAAkB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;SAC1D;QAED,OAAO,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC;QAErC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YAChC,OAAO,IAAI,cAAc,KAAK,GAAG,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;YACvE,OAAO,IAAI,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC;YAC3E,OAAO,IAAI,GAAG,OAAO,CAAC,OAAO,MAAM,CAAC;YACpC,OAAO,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAxPD,wCAwPC"}