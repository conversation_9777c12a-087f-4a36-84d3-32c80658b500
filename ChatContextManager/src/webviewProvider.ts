import * as vscode from 'vscode';
import * as path from 'path';
import { SessionManager, ChatSession } from './sessionManager';

export class WebviewProvider {
    private panel: vscode.WebviewPanel | undefined;

    constructor(
        private context: vscode.ExtensionContext,
        private sessionManager: SessionManager
    ) {}

    public showSessions() {
        if (this.panel) {
            this.panel.reveal();
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            'chatSessions',
            'Chat Sessions',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        this.panel.webview.html = this.getWebviewContent();
        
        this.panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'getSessions':
                        const allSessions = await this.sessionManager.getAllSessions();
                        this.panel?.webview.postMessage({
                            command: 'sessionsData',
                            sessions: allSessions
                        });
                        break;

                    case 'deleteSession':
                        await this.sessionManager.deleteSession(message.sessionId);
                        vscode.window.showInformationMessage('Session deleted successfully!');
                        // Refresh the sessions list
                        const updatedSessions = await this.sessionManager.getAllSessions();
                        this.panel?.webview.postMessage({
                            command: 'sessionsData',
                            sessions: updatedSessions
                        });
                        break;

                    case 'generateMegaPrompt':
                        const sessionsForPrompt = await this.sessionManager.getAllSessions();
                        const targetSession = sessionsForPrompt.find(s => s.id === message.sessionId);
                        if (targetSession) {
                            const megaPrompt = this.sessionManager.generateMegaPrompt(targetSession);
                            await vscode.env.clipboard.writeText(megaPrompt);
                            vscode.window.showInformationMessage('Mega prompt copied to clipboard!');
                        }
                        break;

                    case 'viewDetails':
                        const allSessionsForDetails = await this.sessionManager.getAllSessions();
                        const sessionForDetails = allSessionsForDetails.find(s => s.id === message.sessionId);
                        if (sessionForDetails) {
                            this.showSessionDetails(sessionForDetails);
                        }
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.panel.onDidDispose(() => {
            this.panel = undefined;
        });
    }

    private showSessionDetails(session: ChatSession) {
        let details = `Session: ${session.name}\n`;
        details += `Date: ${new Date(session.timestamp).toLocaleString()}\n`;
        details += `Messages: ${session.messages.length}\n`;

        if (session.requestIds && session.requestIds.length > 0) {
            details += `Request IDs: ${session.requestIds.join(', ')}\n`;
        }

        if (session.txtFilePath) {
            details += `TXT File: ${session.txtFilePath}\n`;
        }

        details += '\n--- MESSAGES ---\n\n';

        session.messages.forEach((msg, index) => {
            details += `${index + 1}. ${msg.role.toUpperCase()}:\n`;
            details += `${msg.content}\n\n`;
            details += '---\n\n';
        });

        vscode.window.showInformationMessage(
            `Session Details:\n${details.substring(0, 500)}${details.length > 500 ? '...\n\n[Full details saved in TXT file]' : ''}`,
            { modal: true },
            'OK'
        );
    }

    private getWebviewContent(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Sessions</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
        }
        
        .session-item {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: var(--vscode-editor-background);
        }
        
        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .session-name {
            font-size: 16px;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        
        .session-date {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        
        .session-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .delete-btn {
            background-color: var(--vscode-errorForeground);
        }
        
        .delete-btn:hover {
            background-color: var(--vscode-errorForeground);
            opacity: 0.8;
        }
        
        .session-details {
            margin-bottom: 10px;
        }

        .message-count, .request-ids, .txt-file {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 5px;
        }

        .messages-preview {
            background-color: var(--vscode-textBlockQuote-background);
            border-left: 3px solid var(--vscode-textBlockQuote-border);
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .message-preview {
            font-size: 12px;
            margin-bottom: 5px;
            color: var(--vscode-editor-foreground);
        }

        .message-preview strong {
            color: var(--vscode-textLink-foreground);
        }

        .more-messages {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }
        
        .no-sessions {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
            margin-top: 50px;
        }
        
        .loading {
            text-align: center;
            color: var(--vscode-descriptionForeground);
        }
    </style>
</head>
<body>
    <h1>💬 Saved Chat Sessions</h1>
    <div id="sessions-container">
        <div class="loading">Loading sessions...</div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        // Request sessions data when page loads
        vscode.postMessage({ command: 'getSessions' });
        
        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            if (message.command === 'sessionsData') {
                displaySessions(message.sessions);
            }
        });
        
        function displaySessions(sessions) {
            const container = document.getElementById('sessions-container');
            
            if (sessions.length === 0) {
                container.innerHTML = '<div class="no-sessions">No saved sessions found. Save a chat session to get started!</div>';
                return;
            }
            
            container.innerHTML = sessions.map(session =>
                \`<div class="session-item">
                    <div class="session-header">
                        <div class="session-name">\${session.name}</div>
                        <div class="session-date">\${new Date(session.timestamp).toLocaleString()}</div>
                    </div>
                    <div class="session-details">
                        <div class="message-count">📝 \${session.messages.length} messages</div>
                        \${session.requestIds && session.requestIds.length > 0 ?
                            \`<div class="request-ids">🔗 \${session.requestIds.length} request IDs</div>\` : ''
                        }
                        \${session.txtFilePath ?
                            \`<div class="txt-file">📄 TXT file saved</div>\` : ''
                        }
                    </div>
                    <div class="messages-preview">
                        \${session.messages.slice(0, 2).map((msg, idx) =>
                            \`<div class="message-preview">
                                <strong>\${msg.role.toUpperCase()}:</strong>
                                \${msg.content.substring(0, 80)}\${msg.content.length > 80 ? '...' : ''}
                            </div>\`
                        ).join('')}
                        \${session.messages.length > 2 ? \`<div class="more-messages">... and \${session.messages.length - 2} more messages</div>\` : ''}
                    </div>
                    <div class="session-actions">
                        <button onclick="generateMegaPrompt('\${session.id}')">📋 Copy Mega Prompt</button>
                        <button onclick="viewDetails('\${session.id}')">👁️ View Details</button>
                        <button class="delete-btn" onclick="deleteSession('\${session.id}')">🗑️ Delete</button>
                    </div>
                </div>\`
            ).join('');
        }
        
        function generateMegaPrompt(sessionId) {
            vscode.postMessage({ 
                command: 'generateMegaPrompt', 
                sessionId: sessionId 
            });
        }
        
        function deleteSession(sessionId) {
            if (confirm('Are you sure you want to delete this session?')) {
                vscode.postMessage({
                    command: 'deleteSession',
                    sessionId: sessionId
                });
            }
        }

        function viewDetails(sessionId) {
            vscode.postMessage({
                command: 'viewDetails',
                sessionId: sessionId
            });
        }
    </script>
</body>
</html>`;
    }
}
