"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const sessionManager_1 = require("./sessionManager");
const chatMonitor_1 = require("./chatMonitor");
const webviewProvider_1 = require("./webviewProvider");
function activate(context) {
    console.log('Chat Context Manager is now active!');
    const sessionManager = new sessionManager_1.SessionManager(context);
    const chatMonitor = new chatMonitor_1.ChatMonitor();
    const webviewProvider = new webviewProvider_1.WebviewProvider(context, sessionManager);
    // Register commands
    const saveSessionCommand = vscode.commands.registerCommand('chatContextManager.saveSession', async () => {
        try {
            const chatContent = await chatMonitor.getCurrentChatContent();
            if (chatContent) {
                // Parse and preview messages before saving
                const messages = sessionManager.previewMessages(chatContent);
                if (messages.length === 0) {
                    vscode.window.showWarningMessage('No valid chat messages found in the content. Please check the format.');
                    return;
                }
                // Show preview of parsed messages
                const previewText = messages.map((msg, index) => `${index + 1}. ${msg.role.toUpperCase()}: ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`).join('\n');
                const shouldSave = await vscode.window.showInformationMessage(`Found ${messages.length} messages. Preview:\n\n${previewText}`, { modal: true }, 'Save Session', 'Cancel');
                if (shouldSave === 'Save Session') {
                    const sessionName = await vscode.window.showInputBox({
                        prompt: 'Enter a name for this chat session',
                        placeHolder: 'My Chat Session'
                    });
                    if (sessionName) {
                        const savedSession = await sessionManager.saveSession(sessionName, chatContent);
                        vscode.window.showInformationMessage(`Session "${sessionName}" saved successfully!\n` +
                            `Messages: ${messages.length}\n` +
                            `TXT file saved in savesmsg folder`);
                    }
                }
            }
            else {
                const choice = await vscode.window.showWarningMessage('No chat content found. Please copy your chat conversation to clipboard or select text in editor first.', 'Show Instructions', 'Try Again');
                if (choice === 'Show Instructions') {
                    vscode.window.showInformationMessage('How to save chat: 1) Copy your chat conversation, 2) Run "Save Chat Session" command, OR select chat text in VSCode editor first.');
                }
                else if (choice === 'Try Again') {
                    vscode.commands.executeCommand('chatContextManager.saveSession');
                }
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to save session: ${error}`);
        }
    });
    const showSessionsCommand = vscode.commands.registerCommand('chatContextManager.showSessions', () => {
        webviewProvider.showSessions();
    });
    const generateMegaPromptCommand = vscode.commands.registerCommand('chatContextManager.generateMegaPrompt', async () => {
        const sessions = await sessionManager.getAllSessions();
        if (sessions.length === 0) {
            vscode.window.showInformationMessage('No saved sessions found.');
            return;
        }
        const sessionItems = sessions.map(session => ({
            label: session.name,
            description: new Date(session.timestamp).toLocaleString(),
            session: session
        }));
        const selectedItem = await vscode.window.showQuickPick(sessionItems, {
            placeHolder: 'Select a session to generate mega prompt'
        });
        if (selectedItem) {
            const megaPrompt = sessionManager.generateMegaPrompt(selectedItem.session);
            await vscode.env.clipboard.writeText(megaPrompt);
            vscode.window.showInformationMessage('Mega prompt copied to clipboard!');
        }
    });
    context.subscriptions.push(saveSessionCommand, showSessionsCommand, generateMegaPromptCommand);
}
exports.activate = activate;
function deactivate() { }
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map