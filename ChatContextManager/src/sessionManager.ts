import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface ChatMessage {
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
}

export interface ChatSession {
    id: string;
    name: string;
    messages: ChatMessage[];
    timestamp: number;
    summary?: string;
    requestIds?: string[];
    txtFilePath?: string;
}

export class SessionManager {
    private sessionsFilePath: string;
    private savesMsgDir: string;

    constructor(private context: vscode.ExtensionContext) {
        this.sessionsFilePath = path.join(context.globalStorageUri.fsPath, 'sessions.json');
        this.savesMsgDir = path.join(context.globalStorageUri.fsPath, 'savesmsg');
        this.ensureStorageDirectory();
    }

    private ensureStorageDirectory() {
        const storageDir = path.dirname(this.sessionsFilePath);
        if (!fs.existsSync(storageDir)) {
            fs.mkdirSync(storageDir, { recursive: true });
        }

        // Ensure savesmsg directory exists
        if (!fs.existsSync(this.savesMsgDir)) {
            fs.mkdirSync(this.savesMsgDir, { recursive: true });
        }
    }

    async saveSession(name: string, chatContent: string): Promise<ChatSession> {
        const messages = this.parseChatContent(chatContent);
        const requestIds = this.extractRequestIds(chatContent);

        // Create TXT file
        const txtFileName = `${this.generateId()}_${name.replace(/[^a-zA-Z0-9]/g, '_')}.txt`;
        const txtFilePath = path.join(this.savesMsgDir, txtFileName);

        // Format content for TXT file
        const formattedContent = this.formatForTxtFile(messages, requestIds, name);
        fs.writeFileSync(txtFilePath, formattedContent, 'utf8');

        const session: ChatSession = {
            id: this.generateId(),
            name,
            messages,
            timestamp: Date.now(),
            requestIds,
            txtFilePath
        };

        const sessions = await this.getAllSessions();
        sessions.push(session);

        await this.saveSessions(sessions);
        return session;
    }

    parseAndPreviewMessages(chatContent: string): ChatMessage[] {
        return this.parseChatContent(chatContent);
    }

    async getAllSessions(): Promise<ChatSession[]> {
        try {
            if (!fs.existsSync(this.sessionsFilePath)) {
                return [];
            }
            
            const data = fs.readFileSync(this.sessionsFilePath, 'utf8');
            return JSON.parse(data) || [];
        } catch (error) {
            console.error('Error reading sessions:', error);
            return [];
        }
    }

    async deleteSession(sessionId: string): Promise<void> {
        const sessions = await this.getAllSessions();
        const filteredSessions = sessions.filter(s => s.id !== sessionId);
        await this.saveSessions(filteredSessions);
    }

    generateMegaPrompt(session: ChatSession): string {
        let prompt = `# Previous Chat Context\n\n`;
        prompt += `Session: ${session.name}\n`;
        prompt += `Date: ${new Date(session.timestamp).toLocaleString()}\n\n`;
        
        prompt += `## Conversation History:\n\n`;
        
        session.messages.forEach((message, index) => {
            const role = message.role === 'user' ? 'User' : 'Assistant';
            prompt += `**${role}:** ${message.content}\n\n`;
        });
        
        prompt += `## Instructions:\n`;
        prompt += `Please continue this conversation with the same context and understanding. `;
        prompt += `Maintain the same tone and refer back to previous points discussed as needed.\n\n`;
        
        return prompt;
    }

    private parseChatContent(content: string): ChatMessage[] {
        const messages: ChatMessage[] = [];
        const lines = content.split('\n');
        let currentMessage: Partial<ChatMessage> = {};
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            
            // Simple parsing - look for patterns that indicate user vs assistant messages
            if (trimmedLine.toLowerCase().startsWith('user:') || trimmedLine.startsWith('👤')) {
                if (currentMessage.content) {
                    messages.push(currentMessage as ChatMessage);
                }
                currentMessage = {
                    role: 'user',
                    content: trimmedLine.replace(/^(user:|👤)\s*/i, ''),
                    timestamp: Date.now()
                };
            } else if (trimmedLine.toLowerCase().startsWith('assistant:') || trimmedLine.startsWith('🤖')) {
                if (currentMessage.content) {
                    messages.push(currentMessage as ChatMessage);
                }
                currentMessage = {
                    role: 'assistant',
                    content: trimmedLine.replace(/^(assistant:|🤖)\s*/i, ''),
                    timestamp: Date.now()
                };
            } else if (trimmedLine && currentMessage.role) {
                currentMessage.content += '\n' + trimmedLine;
            }
        }
        
        if (currentMessage.content) {
            messages.push(currentMessage as ChatMessage);
        }
        
        return messages;
    }

    private async saveSessions(sessions: ChatSession[]): Promise<void> {
        try {
            fs.writeFileSync(this.sessionsFilePath, JSON.stringify(sessions, null, 2));
        } catch (error) {
            throw new Error(`Failed to save sessions: ${error}`);
        }
    }

    private generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    private extractRequestIds(content: string): string[] {
        const requestIds: string[] = [];
        // Look for UUID patterns (like the ones you mentioned)
        const uuidPattern = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi;
        const matches = content.match(uuidPattern);

        if (matches) {
            requestIds.push(...matches);
        }

        return [...new Set(requestIds)]; // Remove duplicates
    }

    private formatForTxtFile(messages: ChatMessage[], requestIds: string[], sessionName: string): string {
        let content = `# Chat Session: ${sessionName}\n`;
        content += `# Date: ${new Date().toLocaleString()}\n`;
        content += `# Messages: ${messages.length}\n`;

        if (requestIds.length > 0) {
            content += `# Request IDs: ${requestIds.join(', ')}\n`;
        }

        content += `\n${'='.repeat(50)}\n\n`;

        messages.forEach((message, index) => {
            content += `## Message ${index + 1} - ${message.role.toUpperCase()}\n`;
            content += `**Time:** ${new Date(message.timestamp).toLocaleString()}\n\n`;
            content += `${message.content}\n\n`;
            content += `${'-'.repeat(30)}\n\n`;
        });

        return content;
    }
}
