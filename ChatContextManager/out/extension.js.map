{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,qDAAkD;AAClD,+CAA4C;AAC5C,uDAAoD;AAEpD,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,MAAM,cAAc,GAAG,IAAI,+BAAc,CAAC,OAAO,CAAC,CAAC;IACnD,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;IACtC,MAAM,eAAe,GAAG,IAAI,iCAAe,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IAErE,oBAAoB;IACpB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;QACpG,IAAI;YACA,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,qBAAqB,EAAE,CAAC;YAC9D,IAAI,WAAW,EAAE;gBACb,2CAA2C;gBAC3C,MAAM,QAAQ,GAAG,cAAc,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;gBAErE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,uEAAuE,CAAC,CAAC;oBAC1G,OAAO;iBACV;gBAED,kCAAkC;gBAClC,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAC5C,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CACtH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEb,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACzD,SAAS,QAAQ,CAAC,MAAM,0BAA0B,WAAW,EAAE,EAC/D,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,cAAc,EACd,QAAQ,CACX,CAAC;gBAEF,IAAI,UAAU,KAAK,cAAc,EAAE;oBAC/B,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;wBACjD,MAAM,EAAE,oCAAoC;wBAC5C,WAAW,EAAE,iBAAiB;qBACjC,CAAC,CAAC;oBAEH,IAAI,WAAW,EAAE;wBACb,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;wBAChF,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,YAAY,WAAW,yBAAyB;4BAChD,aAAa,QAAQ,CAAC,MAAM,IAAI;4BAChC,mCAAmC,CACtC,CAAC;qBACL;iBACJ;aACJ;iBAAM;gBACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACjD,wGAAwG,EACxG,mBAAmB,EACnB,WAAW,CACd,CAAC;gBAEF,IAAI,MAAM,KAAK,mBAAmB,EAAE;oBAChC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,mIAAmI,CACtI,CAAC;iBACL;qBAAM,IAAI,MAAM,KAAK,WAAW,EAAE;oBAC/B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;iBACpE;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACtE;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAChG,eAAe,CAAC,YAAY,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;QAClH,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,cAAc,EAAE,CAAC;QACvD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC;YACjE,OAAO;SACV;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC1C,KAAK,EAAE,OAAO,CAAC,IAAI;YACnB,WAAW,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE;YACzD,OAAO,EAAE,OAAO;SACnB,CAAC,CAAC,CAAC;QAEJ,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE;YACjE,WAAW,EAAE,0CAA0C;SAC1D,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE;YACd,MAAM,UAAU,GAAG,cAAc,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC3E,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;SAC5E;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,kBAAkB,EAClB,mBAAmB,EACnB,yBAAyB,CAC5B,CAAC;AACN,CAAC;AApGD,4BAoGC;AAED,SAAgB,UAAU,KAAI,CAAC;AAA/B,gCAA+B"}